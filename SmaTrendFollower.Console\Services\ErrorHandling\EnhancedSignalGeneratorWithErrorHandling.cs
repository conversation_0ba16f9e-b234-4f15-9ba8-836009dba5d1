using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services.ErrorHandling;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with comprehensive error handling and resilience
/// </summary>
public sealed class EnhancedSignalGeneratorWithErrorHandling : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly IEnhancedRetryService _retryService;
    private readonly IErrorHandler _errorHandler;
    private readonly ILogger<EnhancedSignalGeneratorWithErrorHandling> _logger;
    private readonly SemaphoreSlim _batchSemaphore;
    private readonly ConcurrentDictionary<string, DateTime> _symbolErrorTracker = new();

    public EnhancedSignalGeneratorWithErrorHandling(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        IEnhancedRetryService retryService,
        IErrorHandler errorHandler,
        ILogger<EnhancedSignalGeneratorWithErrorHandling> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _retryService = retryService;
        _errorHandler = errorHandler;
        _logger = logger;
        _batchSemaphore = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        var context = new ErrorContext
        {
            OperationName = nameof(RunAsync),
            ServiceName = nameof(EnhancedSignalGeneratorWithErrorHandling)
        }.WithProperty("TopN", topN);

        try
        {
            return await _retryService.ExecuteAsync(
                async () => await GenerateSignalsInternalAsync(topN),
                "GenerateSignals",
                "SignalGeneration");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            if (result.Strategy == RecoveryStrategy.Fallback)
            {
                _logger.LogWarning("Using fallback signal generation strategy");
                return await GenerateFallbackSignalsAsync(topN);
            }
            
            if (result.Strategy == RecoveryStrategy.Skip)
            {
                _logger.LogWarning("Skipping signal generation due to error");
                return Enumerable.Empty<TradingSignal>();
            }

            throw new MarketDataException(
                "Failed to generate trading signals after all retry attempts",
                isRetriable: false,
                innerException: ex);
        }
    }

    private async Task<IEnumerable<TradingSignal>> GenerateSignalsInternalAsync(int topN)
    {
        // Get universe of symbols with error handling
        var symbols = await GetUniverseWithErrorHandlingAsync();
        var symbolList = symbols.ToList();

        if (symbolList.Count == 0)
        {
            throw new MarketDataException("No symbols available from universe provider");
        }

        _logger.LogInformation("Screening {Count} symbols for trading signals", symbolList.Count);

        // Filter out symbols that have had recent errors
        var eligibleSymbols = FilterEligibleSymbols(symbolList);
        _logger.LogInformation("Processing {EligibleCount} eligible symbols (filtered {FilteredCount} with recent errors)",
            eligibleSymbols.Count, symbolList.Count - eligibleSymbols.Count);

        var signals = new ConcurrentBag<TradingSignal>();
        var processedCount = 0;
        var errorCount = 0;

        // Process symbols in parallel batches with error handling
        const int batchSize = 5; // Smaller batches for better error isolation
        var batches = eligibleSymbols.Chunk(batchSize);

        await Parallel.ForEachAsync(batches, new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = CancellationToken.None
        }, async (batch, ct) =>
        {
            await _batchSemaphore.WaitAsync(ct);
            try
            {
                var batchSignals = await ProcessBatchWithErrorHandling(batch, ct);
                foreach (var signal in batchSignals)
                {
                    signals.Add(signal);
                }
                Interlocked.Add(ref processedCount, batch.Length);
            }
            catch (Exception ex)
            {
                Interlocked.Increment(ref errorCount);
                _logger.LogWarning(ex, "Batch processing failed for {BatchSize} symbols", batch.Length);
            }
            finally
            {
                _batchSemaphore.Release();
            }
        });

        _logger.LogInformation("Processed {ProcessedCount} symbols with {ErrorCount} batch errors",
            processedCount, errorCount);

        // Filter and rank signals with validation
        var validSignals = ValidateAndFilterSignals(signals);
        var rankedSignals = RankAndSelectSignals(validSignals, topN);

        _logger.LogInformation("Generated {Count} trading signals from {Total} symbols",
            rankedSignals.Count, symbolList.Count);

        return rankedSignals;
    }

    private async Task<IEnumerable<string>> GetUniverseWithErrorHandlingAsync()
    {
        var context = new ErrorContext
        {
            OperationName = "GetUniverse",
            ServiceName = nameof(EnhancedSignalGeneratorWithErrorHandling)
        };

        try
        {
            return await _retryService.ExecuteAsync(
                async () => await _universeProvider.GetSymbolsAsync(),
                "GetUniverse",
                "UniverseProvider");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            if (result.Strategy == RecoveryStrategy.Fallback)
            {
                _logger.LogWarning("Using fallback universe (SPY only)");
                return new[] { "SPY" };
            }

            throw new MarketDataException(
                "Failed to retrieve symbol universe",
                isRetriable: true,
                suggestedRetryDelay: TimeSpan.FromMinutes(1),
                innerException: ex);
        }
    }

    private List<string> FilterEligibleSymbols(List<string> symbols)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-30); // Skip symbols with errors in last 30 minutes
        
        return symbols.Where(symbol =>
        {
            if (_symbolErrorTracker.TryGetValue(symbol, out var lastErrorTime))
            {
                return lastErrorTime < cutoffTime;
            }
            return true;
        }).ToList();
    }

    private async Task<IEnumerable<TradingSignal>> ProcessBatchWithErrorHandling(
        string[] batch,
        CancellationToken cancellationToken)
    {
        var signals = new List<TradingSignal>();

        foreach (var symbol in batch)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                var signal = await ProcessSymbolWithErrorHandling(symbol);
                if (signal != null)
                {
                    signals.Add(signal);
                }
            }
            catch (Exception ex)
            {
                RecordSymbolError(symbol);
                _logger.LogWarning(ex, "Failed to process symbol {Symbol}", symbol);
            }
        }

        return signals;
    }

    private async Task<TradingSignal?> ProcessSymbolWithErrorHandling(string symbol)
    {
        var context = new ErrorContext
        {
            OperationName = "ProcessSymbol",
            ServiceName = nameof(EnhancedSignalGeneratorWithErrorHandling)
        }.WithProperty("Symbol", symbol);

        try
        {
            return await _retryService.ExecuteAsync(
                async () => await ProcessSymbolInternal(symbol),
                $"ProcessSymbol-{symbol}",
                "MarketData");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            if (result.Strategy == RecoveryStrategy.Skip)
            {
                _logger.LogDebug("Skipping symbol {Symbol} due to error: {Error}", symbol, ex.Message);
                return null;
            }

            // For critical errors, record and skip
            RecordSymbolError(symbol);
            return null;
        }
    }

    private async Task<TradingSignal?> ProcessSymbolInternal(string symbol)
    {
        // Get 250 days of data for technical analysis
        var startDate = DateTime.UtcNow.AddDays(-300);
        var endDate = DateTime.UtcNow;

        var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
        var bars = response.Items.ToList();

        if (bars.Count < 200) // Need enough data for SMA200
        {
            throw new MarketDataException($"Insufficient data for {symbol}: {bars.Count} bars", symbol);
        }

        var currentPrice = bars.Last().Close;
        var sma50 = (decimal)bars.GetSma50();
        var sma200 = (decimal)bars.GetSma200();
        var atr14 = (decimal)bars.GetAtr14();
        var sixMonthReturn = (decimal)bars.GetTotalReturn(126); // ~6 months

        // Validate calculated values
        if (currentPrice <= 0 || sma50 <= 0 || sma200 <= 0 || atr14 <= 0)
        {
            throw new MarketDataException($"Invalid calculated values for {symbol}", symbol);
        }

        // Filter: close > sma50 && close > sma200
        if (currentPrice > sma50 && currentPrice > sma200)
        {
            return new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn);
        }

        return null; // Signal doesn't meet criteria
    }

    private List<TradingSignal> ValidateAndFilterSignals(ConcurrentBag<TradingSignal> signals)
    {
        return signals
            .Where(s => s != null)
            .Where(s => s.Price > 0 && s.Atr > 0)
            .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3%
            .Where(s => !string.IsNullOrEmpty(s.Symbol))
            .ToList();
    }

    private List<TradingSignal> RankAndSelectSignals(List<TradingSignal> signals, int topN)
    {
        return signals
            .OrderByDescending(s => s.SixMonthReturn)
            .Take(topN)
            .ToList();
    }

    private async Task<IEnumerable<TradingSignal>> GenerateFallbackSignalsAsync(int topN)
    {
        _logger.LogInformation("Generating fallback signals for SPY only");
        
        try
        {
            var signal = await ProcessSymbolInternal("SPY");
            return signal != null ? new[] { signal } : Enumerable.Empty<TradingSignal>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fallback signal generation failed");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    private void RecordSymbolError(string symbol)
    {
        _symbolErrorTracker[symbol] = DateTime.UtcNow;
        
        // Clean up old entries periodically
        if (_symbolErrorTracker.Count > 1000)
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-2);
            var keysToRemove = _symbolErrorTracker
                .Where(kvp => kvp.Value < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _symbolErrorTracker.TryRemove(key, out _);
            }
        }
    }
}
