﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[Collection("EnvironmentVariableTests")]
public class SafetyConfigurationServiceTests
{
    private readonly Mock<ILogger<SafetyConfigurationService>> _mockLogger;
    private readonly SafetyConfigurationService _configService;

    public SafetyConfigurationServiceTests()
    {
        _mockLogger = new Mock<ILogger<SafetyConfigurationService>>();
        _configService = new SafetyConfigurationService(_mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void CreateSafeDefaults_ShouldReturnSafeConfiguration()
    {
        // Act
        var config = _configService.CreateSafeDefaults();

        // Assert
        config.MaxDailyLoss.Should().Be(500m);
        config.MaxPositionSizePercent.Should().Be(0.05m);
        config.MaxPositions.Should().Be(10);
        config.MaxDailyTrades.Should().Be(20);
        config.MinAccountEquity.Should().Be(1000m);
        config.MaxSingleTradeValue.Should().Be(2000m);
        config.RequireConfirmation.Should().BeTrue();
        config.DryRunMode.Should().BeFalse();
        config.AllowedEnvironment.Should().Be(TradingEnvironment.Paper);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void CreatePaperTradingConfiguration_ShouldReturnPaperConfig()
    {
        // Act
        var config = _configService.CreatePaperTradingConfiguration();

        // Assert
        config.MaxDailyLoss.Should().Be(1000m);
        config.MaxPositionSizePercent.Should().Be(0.10m);
        config.MaxPositions.Should().Be(20);
        config.MaxDailyTrades.Should().Be(50);
        config.MinAccountEquity.Should().Be(100m);
        config.MaxSingleTradeValue.Should().Be(5000m);
        config.RequireConfirmation.Should().BeFalse();
        config.DryRunMode.Should().BeFalse();
        config.AllowedEnvironment.Should().Be(TradingEnvironment.Paper);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void CreateLiveTradingConfiguration_ShouldReturnLiveConfig()
    {
        // Act
        var config = _configService.CreateLiveTradingConfiguration();

        // Assert
        config.MaxDailyLoss.Should().Be(250m);
        config.MaxPositionSizePercent.Should().Be(0.03m);
        config.MaxPositions.Should().Be(8);
        config.MaxDailyTrades.Should().Be(15);
        config.MinAccountEquity.Should().Be(5000m);
        config.MaxSingleTradeValue.Should().Be(1500m);
        config.RequireConfirmation.Should().BeTrue();
        config.DryRunMode.Should().BeFalse();
        config.AllowedEnvironment.Should().Be(TradingEnvironment.Live);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithValidConfig_ShouldReturnValid()
    {
        // Arrange
        var config = new SafetyConfiguration
        {
            MaxDailyLoss = 500m,
            MaxPositionSizePercent = 0.05m,
            MaxPositions = 10,
            MaxDailyTrades = 20,
            MinAccountEquity = 1000m,
            MaxSingleTradeValue = 2000m
        };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeTrue();
        errors.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithInvalidMaxDailyLoss_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MaxDailyLoss = -100m };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MaxDailyLoss must be greater than 0");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithInvalidPositionSizePercent_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MaxPositionSizePercent = 1.5m }; // 150%

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MaxPositionSizePercent must be between 0 and 1");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithZeroPositions_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MaxPositions = 0 };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MaxPositions must be greater than 0");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithZeroDailyTrades_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MaxDailyTrades = 0 };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MaxDailyTrades must be greater than 0");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithNegativeMinEquity_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MinAccountEquity = -1000m };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MinAccountEquity cannot be negative");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithZeroTradeValue_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SafetyConfiguration { MaxSingleTradeValue = 0m };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("MaxSingleTradeValue must be greater than 0");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void ValidateConfiguration_WithMultipleErrors_ShouldReturnAllErrors()
    {
        // Arrange
        var config = new SafetyConfiguration
        {
            MaxDailyLoss = -100m,
            MaxPositionSizePercent = 2m,
            MaxPositions = 0,
            MaxDailyTrades = -5,
            MinAccountEquity = -1000m,
            MaxSingleTradeValue = 0m
        };

        // Act
        var (isValid, errors) = _configService.ValidateConfiguration(config);

        // Assert
        isValid.Should().BeFalse();
        errors.Should().HaveCount(6);
        errors.Should().Contain("MaxDailyLoss must be greater than 0");
        errors.Should().Contain("MaxPositionSizePercent must be between 0 and 1");
        errors.Should().Contain("MaxPositions must be greater than 0");
        errors.Should().Contain("MaxDailyTrades must be greater than 0");
        errors.Should().Contain("MinAccountEquity cannot be negative");
        errors.Should().Contain("MaxSingleTradeValue must be greater than 0");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Fact]
    public void LoadConfiguration_WithInvalidEnvironmentConfig_ShouldReturnSafeDefaults()
    {
        // Arrange
        Environment.SetEnvironmentVariable("SAFETY_MAX_DAILY_LOSS", "-100"); // Invalid

        try
        {
            // Act
            var config = _configService.LoadConfiguration();

            // Assert
            config.Should().BeEquivalentTo(_configService.CreateSafeDefaults());
        }
        finally
        {
            // Cleanup
            Environment.SetEnvironmentVariable("SAFETY_MAX_DAILY_LOSS", null);
        }
    }
}
