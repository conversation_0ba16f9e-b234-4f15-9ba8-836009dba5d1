using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class RiskManager : IRiskManager
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<RiskManager> _logger;

    public RiskManager(
        IAlpacaClientFactory clientFactory,
        ILogger<RiskManager> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<decimal> CalculateQuantityAsync(TradingSignal signal)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Get account information
                var account = await tradingClient.GetAccountAsync();
                var equity = account.Equity ?? 0m;

                // Calculate dynamic risk parameters based on account size
                var riskParams = CalculateDynamicRiskParameters(equity);

                // Calculate risk dollars using dynamic percentage
                var riskDollars = equity * riskParams.RiskPercentPerTrade;

                // Apply maximum risk cap
                riskDollars = Math.Min(riskDollars, riskParams.MaxRiskPerTrade);

                // Calculate quantity: qty = riskDollars / (atr14 * price)
                var quantity = riskDollars / (signal.Atr * signal.Price);

                // Ensure quantity doesn't exceed risk tolerance: qty <= riskDollars / price
                var maxQuantity = riskDollars / signal.Price;
                quantity = Math.Min(quantity, maxQuantity);

                // Apply position size limit as percentage of equity
                var positionValue = quantity * signal.Price;
                var positionPercent = positionValue / equity;
                if (positionPercent > riskParams.MaxPositionSizePercent)
                {
                    quantity = (equity * riskParams.MaxPositionSizePercent) / signal.Price;
                }

                // Round down to avoid over-allocation
                quantity = Math.Floor(quantity * 100) / 100; // Round to 2 decimal places

                _logger.LogInformation("Dynamic risk calculation for {Symbol}: Equity={Equity:C}, " +
                                     "RiskPercent={RiskPercent:P3}, RiskDollars={RiskDollars:C}, " +
                                     "Price={Price:C}, ATR={ATR:C}, Quantity={Quantity}, " +
                                     "PositionValue={PositionValue:C}, PositionPercent={PositionPercent:P2}",
                    signal.Symbol, equity, riskParams.RiskPercentPerTrade, riskDollars,
                    signal.Price, signal.Atr, quantity, quantity * signal.Price,
                    (quantity * signal.Price) / equity);

                return Math.Max(0, quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating position size for {Symbol}", signal.Symbol);
                return 0;
            }
        }, $"CalculateQuantity-{signal.Symbol}");
    }

    /// <summary>
    /// Calculates dynamic risk parameters based on account size
    /// </summary>
    private DynamicRiskParameters CalculateDynamicRiskParameters(decimal equity)
    {
        // Progressive risk scaling based on account size
        var (riskPercent, maxRisk, maxPositionPercent) = equity switch
        {
            // Very small accounts (under $5k) - higher risk tolerance
            < 5000m => (0.015m, 75m, 0.08m),      // 1.5% risk, $75 max, 8% position max

            // Small accounts ($5k - $15k) - moderate risk
            < 15000m => (0.012m, 150m, 0.06m),    // 1.2% risk, $150 max, 6% position max

            // Medium accounts ($15k - $50k) - balanced risk
            < 50000m => (0.010m, 400m, 0.05m),    // 1.0% risk, $400 max, 5% position max

            // Large accounts ($50k - $100k) - conservative risk
            < 100000m => (0.008m, 600m, 0.04m),   // 0.8% risk, $600 max, 4% position max

            // Very large accounts ($100k+) - very conservative
            _ => (0.006m, 800m, 0.03m)             // 0.6% risk, $800 max, 3% position max
        };

        return new DynamicRiskParameters(riskPercent, maxRisk, maxPositionPercent);
    }
}

/// <summary>
/// Dynamic risk parameters based on account size
/// </summary>
public record DynamicRiskParameters(
    decimal RiskPercentPerTrade,
    decimal MaxRiskPerTrade,
    decimal MaxPositionSizePercent
);
