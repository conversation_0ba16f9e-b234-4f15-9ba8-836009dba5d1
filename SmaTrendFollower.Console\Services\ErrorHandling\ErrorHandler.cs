using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Net;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Centralized error handler implementation with comprehensive error management
/// </summary>
public sealed class ErrorHandler : IErrorHandler
{
    private readonly ILogger<ErrorHandler> _logger;
    private readonly ErrorHandlerOptions _options;
    private readonly ConcurrentDictionary<Type, object> _customHandlers;
    private readonly ConcurrentDictionary<ErrorCategory, object> _categoryHandlers;
    private readonly ConcurrentDictionary<string, CircuitBreakerState> _circuitBreakers;
    private readonly Random _random = new();

    public ErrorHandler(ILogger<ErrorHandler> logger, IOptions<ErrorHandlerOptions> options)
    {
        _logger = logger;
        _options = options.Value;
        _customHandlers = new ConcurrentDictionary<Type, object>(_options.CustomHandlers);
        _categoryHandlers = new ConcurrentDictionary<ErrorCategory, object>(_options.CategoryHandlers);
        _circuitBreakers = new ConcurrentDictionary<string, CircuitBreakerState>();
    }

    public async Task<ErrorHandlingResult> HandleErrorAsync(Exception exception, ErrorContext context)
    {
        // Log the error first
        await LogErrorAsync(exception, context);

        // Check if it's a trading exception
        if (exception is TradingException tradingException)
        {
            return await HandleTradingErrorAsync(tradingException, context);
        }

        // Check for custom handlers
        var exceptionType = exception.GetType();
        if (_customHandlers.TryGetValue(exceptionType, out var handler))
        {
            var customHandler = (Func<Exception, ErrorContext, Task<ErrorHandlingResult>>)handler;
            return await customHandler(exception, context);
        }

        // Handle common exception types
        return exception switch
        {
            HttpRequestException httpEx => await HandleHttpExceptionAsync(httpEx, context),
            TaskCanceledException timeoutEx => await HandleTimeoutExceptionAsync(timeoutEx, context),
            UnauthorizedAccessException authEx => await HandleAuthenticationExceptionAsync(authEx, context),
            ArgumentException argEx => await HandleArgumentExceptionAsync(argEx, context),
            InvalidOperationException opEx => await HandleInvalidOperationExceptionAsync(opEx, context),
            _ => await HandleGenericExceptionAsync(exception, context)
        };
    }

    public async Task<ErrorHandlingResult> HandleTradingErrorAsync(TradingException tradingException, ErrorContext context)
    {
        await LogErrorAsync(tradingException, context);

        // Check for category-specific handlers
        if (_categoryHandlers.TryGetValue(tradingException.Category, out var handler))
        {
            var categoryHandler = (Func<TradingException, ErrorContext, Task<ErrorHandlingResult>>)handler;
            return await categoryHandler(tradingException, context);
        }

        // Handle based on category and severity
        return tradingException.Category switch
        {
            ErrorCategory.MarketData => await HandleMarketDataErrorAsync(tradingException, context),
            ErrorCategory.TradeExecution => await HandleTradeExecutionErrorAsync(tradingException, context),
            ErrorCategory.RiskManagement => await HandleRiskManagementErrorAsync(tradingException, context),
            ErrorCategory.ExternalApi => await HandleExternalApiErrorAsync(tradingException, context),
            ErrorCategory.Configuration => await HandleConfigurationErrorAsync(tradingException, context),
            ErrorCategory.RateLimit => await HandleRateLimitErrorAsync(tradingException, context),
            _ => await HandleGenericTradingErrorAsync(tradingException, context)
        };
    }

    public void RegisterHandler<T>(Func<T, ErrorContext, Task<ErrorHandlingResult>> handler) where T : Exception
    {
        _customHandlers[typeof(T)] = (Exception ex, ErrorContext ctx) => handler((T)ex, ctx);
    }

    public void RegisterCategoryHandler(ErrorCategory category, Func<TradingException, ErrorContext, Task<ErrorHandlingResult>> handler)
    {
        _categoryHandlers[category] = handler;
    }

    public bool ShouldRetry(Exception exception, int attemptCount)
    {
        if (attemptCount >= _options.MaxRetryAttempts)
            return false;

        return exception switch
        {
            TradingException tradingEx => tradingEx.IsRetriable,
            HttpRequestException => true,
            TaskCanceledException => true,
            _ when IsTransientError(exception) => true,
            _ => false
        };
    }

    public TimeSpan CalculateRetryDelay(Exception exception, int attemptCount)
    {
        var baseDelay = _options.BaseDelayMs;
        
        // Check if exception has suggested delay
        if (exception is TradingException tradingEx && tradingEx.SuggestedRetryDelay.HasValue)
        {
            baseDelay = (int)tradingEx.SuggestedRetryDelay.Value.TotalMilliseconds;
        }

        // Exponential backoff
        var delay = Math.Min(baseDelay * Math.Pow(2, attemptCount - 1), _options.MaxDelayMs);

        // Add jitter if enabled
        if (_options.UseJitter)
        {
            var jitter = _random.Next(0, _options.MaxJitterMs);
            delay += jitter;
        }

        return TimeSpan.FromMilliseconds(delay);
    }

    public async Task LogErrorAsync(Exception exception, ErrorContext context, string? additionalMessage = null)
    {
        var severity = GetErrorSeverity(exception);
        var logLevel = MapSeverityToLogLevel(severity);

        var message = $"Error in {context.ServiceName}.{context.OperationName}";
        if (!string.IsNullOrEmpty(additionalMessage))
        {
            message += $": {additionalMessage}";
        }

        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["ServiceName"] = context.ServiceName,
            ["OperationName"] = context.OperationName,
            ["CorrelationId"] = context.CorrelationId ?? Guid.NewGuid().ToString(),
            ["ErrorCategory"] = exception is TradingException tradingEx ? tradingEx.Category.ToString() : "Unknown",
            ["ErrorSeverity"] = severity.ToString(),
            ["MachineName"] = context.MachineName ?? Environment.MachineName,
            ["ProcessId"] = context.ProcessId ?? Environment.ProcessId.ToString()
        });

        _logger.Log(logLevel, exception, message);

        // Send notification for high severity errors if enabled
        if (_options.EnableNotifications && severity >= _options.NotificationThreshold)
        {
            await SendErrorNotificationAsync(exception, context, severity);
        }
    }

    private async Task<ErrorHandlingResult> HandleMarketDataErrorAsync(TradingException exception, ErrorContext context)
    {
        if (exception.IsRetriable)
        {
            var delay = CalculateRetryDelay(exception, context.GetProperty("AttemptCount", 1));
            return ErrorHandlingResult.Retry(delay, "Market data error - retrying with fallback");
        }

        return ErrorHandlingResult.Fallback("Using cached or alternative data source");
    }

    private async Task<ErrorHandlingResult> HandleTradeExecutionErrorAsync(TradingException exception, ErrorContext context)
    {
        // Trade execution errors are typically not retriable and require immediate attention
        return ErrorHandlingResult.Stop("Trade execution failed - manual intervention required");
    }

    private async Task<ErrorHandlingResult> HandleRiskManagementErrorAsync(TradingException exception, ErrorContext context)
    {
        // Risk management errors should stop trading immediately
        return ErrorHandlingResult.Stop("Risk management violation - trading halted");
    }

    private async Task<ErrorHandlingResult> HandleExternalApiErrorAsync(TradingException exception, ErrorContext context)
    {
        var apiName = context.GetProperty<string>("ApiName") ?? "Unknown";
        
        // Check circuit breaker
        if (_options.EnableCircuitBreaker && IsCircuitBreakerOpen(apiName))
        {
            return ErrorHandlingResult.Handled(RecoveryStrategy.CircuitBreaker, $"Circuit breaker open for {apiName}");
        }

        if (exception.IsRetriable)
        {
            var delay = CalculateRetryDelay(exception, context.GetProperty("AttemptCount", 1));
            return ErrorHandlingResult.Retry(delay, $"External API error for {apiName} - retrying");
        }

        return ErrorHandlingResult.Fallback($"External API {apiName} unavailable - using fallback");
    }

    private async Task<ErrorHandlingResult> HandleConfigurationErrorAsync(TradingException exception, ErrorContext context)
    {
        // Configuration errors are not retriable and require immediate attention
        return ErrorHandlingResult.Stop("Configuration error - system cannot continue");
    }

    private async Task<ErrorHandlingResult> HandleRateLimitErrorAsync(TradingException exception, ErrorContext context)
    {
        var delay = exception.SuggestedRetryDelay ?? TimeSpan.FromSeconds(60);
        return ErrorHandlingResult.Retry(delay, "Rate limit exceeded - waiting before retry");
    }

    private async Task<ErrorHandlingResult> HandleHttpExceptionAsync(HttpRequestException exception, ErrorContext context)
    {
        var delay = CalculateRetryDelay(exception, context.GetProperty("AttemptCount", 1));
        return ErrorHandlingResult.Retry(delay, "HTTP request failed - retrying");
    }

    private async Task<ErrorHandlingResult> HandleTimeoutExceptionAsync(TaskCanceledException exception, ErrorContext context)
    {
        var delay = CalculateRetryDelay(exception, context.GetProperty("AttemptCount", 1));
        return ErrorHandlingResult.Retry(delay, "Request timeout - retrying with longer timeout");
    }

    private async Task<ErrorHandlingResult> HandleAuthenticationExceptionAsync(UnauthorizedAccessException exception, ErrorContext context)
    {
        return ErrorHandlingResult.Stop("Authentication failed - check credentials");
    }

    private async Task<ErrorHandlingResult> HandleArgumentExceptionAsync(ArgumentException exception, ErrorContext context)
    {
        return ErrorHandlingResult.Skip("Invalid argument - skipping operation");
    }

    private async Task<ErrorHandlingResult> HandleInvalidOperationExceptionAsync(InvalidOperationException exception, ErrorContext context)
    {
        return ErrorHandlingResult.Skip("Invalid operation - skipping");
    }

    private async Task<ErrorHandlingResult> HandleGenericExceptionAsync(Exception exception, ErrorContext context)
    {
        var severity = GetErrorSeverity(exception);
        return severity >= ErrorSeverity.High 
            ? ErrorHandlingResult.Stop("Critical error - manual intervention required")
            : ErrorHandlingResult.Skip("Non-critical error - continuing");
    }

    private async Task<ErrorHandlingResult> HandleGenericTradingErrorAsync(TradingException exception, ErrorContext context)
    {
        return exception.Severity >= ErrorSeverity.High
            ? ErrorHandlingResult.Stop($"Critical trading error: {exception.Message}")
            : ErrorHandlingResult.Skip($"Non-critical trading error: {exception.Message}");
    }

    private static ErrorSeverity GetErrorSeverity(Exception exception)
    {
        return exception switch
        {
            TradingException tradingEx => tradingEx.Severity,
            UnauthorizedAccessException => ErrorSeverity.Critical,
            ArgumentException => ErrorSeverity.Low,
            HttpRequestException => ErrorSeverity.Medium,
            TaskCanceledException => ErrorSeverity.Medium,
            _ => ErrorSeverity.Medium
        };
    }

    private static LogLevel MapSeverityToLogLevel(ErrorSeverity severity)
    {
        return severity switch
        {
            ErrorSeverity.Low => LogLevel.Warning,
            ErrorSeverity.Medium => LogLevel.Error,
            ErrorSeverity.High => LogLevel.Error,
            ErrorSeverity.Critical => LogLevel.Critical,
            _ => LogLevel.Error
        };
    }

    private static bool IsTransientError(Exception exception)
    {
        return exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
               exception.Message.Contains("network", StringComparison.OrdinalIgnoreCase) ||
               exception.Message.Contains("connection", StringComparison.OrdinalIgnoreCase);
    }

    private bool IsCircuitBreakerOpen(string serviceName)
    {
        if (!_circuitBreakers.TryGetValue(serviceName, out var state))
            return false;

        if (state.IsOpen && DateTime.UtcNow > state.OpenUntil)
        {
            // Reset circuit breaker
            _circuitBreakers[serviceName] = new CircuitBreakerState();
            return false;
        }

        return state.IsOpen;
    }

    private async Task SendErrorNotificationAsync(Exception exception, ErrorContext context, ErrorSeverity severity)
    {
        // This would integrate with the Discord notification service
        // For now, just log that a notification should be sent
        _logger.LogWarning("High severity error notification should be sent: {ErrorType} in {Service}.{Operation}",
            exception.GetType().Name, context.ServiceName, context.OperationName);
    }
}

/// <summary>
/// Circuit breaker state tracking
/// </summary>
internal sealed record CircuitBreakerState
{
    public bool IsOpen { get; init; }
    public int FailureCount { get; init; }
    public DateTime OpenUntil { get; init; }
}
